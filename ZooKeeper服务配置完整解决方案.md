# ZooKeeper systemd服务配置方案

## 问题描述

ZooKeeper服务可以通过 `./bin/zkServer.sh start` 正常启动，但无法通过systemd管理。systemd启动时出现 `control process exited, code=exited status=1` 错误。

## 问题分析

systemd服务启动失败的主要原因：

1. **环境变量缺失**：systemd运行环境与用户shell环境不同，缺少JAVA_HOME等关键环境变量
2. **工作目录问题**：systemd启动时工作目录可能不正确，导致相对路径失效
3. **权限和路径解析**：systemd对脚本执行的环境要求更严格

## 解决方案：创建启动脚本

### 步骤1：创建启动脚本

创建ZooKeeper启动脚本：

```bash
sudo vim /usr/local/bin/zookeeper-start.sh
```

脚本内容：

```bash
#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
cd /app/zookeeper/zookeeper-3.4.8
./bin/zkServer.sh start
```

设置执行权限：

```bash
sudo chmod +x /usr/local/bin/zookeeper-start.sh
```

### 步骤2：创建停止脚本

创建ZooKeeper停止脚本：

```bash
sudo vim /usr/local/bin/zookeeper-stop.sh
```

脚本内容：

```bash
#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
cd /app/zookeeper/zookeeper-3.4.8
./bin/zkServer.sh stop
```

设置执行权限：

```bash
sudo chmod +x /usr/local/bin/zookeeper-stop.sh
```

### 步骤3：配置systemd服务

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/zookeeper.service
```

服务配置内容：

```ini
[Unit]
Description=Apache ZooKeeper
After=network.target

[Service]
Type=forking
User=root
ExecStart=/usr/local/bin/zookeeper-start.sh
ExecStop=/usr/local/bin/zookeeper-stop.sh
PIDFile=/app/zookeeper/zookeeper-3.4.8/data/zookeeper_server.pid
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 步骤4：启用服务

重新加载systemd配置：

```bash
sudo systemctl daemon-reload
```

停止手动启动的ZooKeeper进程：

```bash
sudo /app/zookeeper/zookeeper-3.4.8/bin/zkServer.sh stop
```

启动systemd服务：

```bash
sudo systemctl start zookeeper
```

检查服务状态：

```bash
sudo systemctl status zookeeper
```

设置开机自启：

```bash
sudo systemctl enable zookeeper
```

### 步骤5：验证服务

检查ZooKeeper进程：

```bash
ps aux | grep zookeeper
```

检查端口监听（端口12181）：

```bash
netstat -tlnp | grep 12181
```

测试ZooKeeper连接：

```bash
echo "ruok" | nc localhost 12181
```

预期返回：`imok`

测试服务重启：

```bash
sudo systemctl restart zookeeper
sudo systemctl status zookeeper
```

## 服务管理命令

systemd服务管理：

```bash
sudo systemctl start zookeeper      # 启动服务
sudo systemctl stop zookeeper       # 停止服务
sudo systemctl restart zookeeper    # 重启服务
sudo systemctl status zookeeper     # 查看状态
sudo systemctl enable zookeeper     # 开机自启
sudo systemctl disable zookeeper    # 禁用自启
```

查看服务日志：

```bash
journalctl -u zookeeper -f          # 实时日志
journalctl -u zookeeper --since today  # 今日日志
```

## 配置文件信息

zoo.cfg配置（端口12181）：

```properties
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/app/zookeeper/zookeeper-3.4.8/data
clientPort=12181
```

## 故障排查

### 服务启动失败

检查systemd服务状态：

```bash
sudo systemctl status zookeeper -l
```

查看详细错误日志：

```bash
journalctl -u zookeeper --no-pager
```

### 脚本执行问题

手动测试启动脚本：

```bash
sudo /usr/local/bin/zookeeper-start.sh
```

检查脚本权限：

```bash
ls -la /usr/local/bin/zookeeper-*.sh
```

### 端口冲突

检查端口占用：

```bash
netstat -tlnp | grep 12181
lsof -i :12181
```

### Java环境问题

验证Java环境：

```bash
java -version
echo $JAVA_HOME
which java
```

### 配置文件检查

验证zoo.cfg语法：

```bash
cat /app/zookeeper/zookeeper-3.4.8/conf/zoo.cfg
```

检查数据目录权限：

```bash
ls -la /app/zookeeper/zookeeper-3.4.8/data/
```

## 方案优势

相比直接在systemd中配置启动命令，使用独立脚本的优势：

1. **环境隔离**：脚本中明确设置所需环境变量，避免systemd环境问题
2. **路径管理**：脚本中切换到正确工作目录，确保相对路径正确解析
3. **调试便利**：可以独立测试脚本，便于问题定位
4. **维护性**：脚本修改不需要重新加载systemd配置
5. **可移植性**：脚本可以在不同环境中复用

此方案解决了systemd环境变量缺失和工作目录问题，实现了ZooKeeper服务的可靠自动化管理。

## systemd定时器延迟启动配置

### 定时器工作原理

systemd定时器可以实现服务的延迟启动，避免系统启动时的资源竞争。

**执行流程**：
```
系统启动 → 定时器激活 → 等待指定时间 → 启动ZooKeeper服务
```

**重启行为**：
- 每次系统重启都会自动执行定时器
- 定时器会在指定延迟后启动服务
- 不需要手动干预

### 创建定时器配置

#### 步骤1：创建定时器文件

```bash
sudo vim /etc/systemd/system/zookeeper.timer
```

#### 步骤2：定时器配置内容

```ini
[Unit]
Description=Delayed start for ZooKeeper
Documentation=https://zookeeper.apache.org/
Requires=zookeeper.service
After=multi-user.target

[Timer]
OnBootSec=60sec
Unit=zookeeper.service
AccuracySec=1sec

[Install]
WantedBy=timers.target
```

#### 步骤3：启用定时器

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 禁用服务的直接自动启动
sudo systemctl disable zookeeper.service

# 启用定时器
sudo systemctl enable zookeeper.timer

# 启动定时器
sudo systemctl start zookeeper.timer
```

#### 步骤4：验证配置

```bash
# 查看定时器状态
sudo systemctl status zookeeper.timer

# 查看所有定时器
sudo systemctl list-timers

# 查看ZooKeeper定时器
sudo systemctl list-timers | grep zookeeper
```

### 定时器参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| OnBootSec | 开机后延迟时间 | 30sec, 60sec, 2min, 5min |
| Unit | 要启动的服务单元 | zookeeper.service |
| AccuracySec | 时间精度 | 1sec（默认1分钟） |
| Requires | 依赖的服务 | zookeeper.service |

### 时间设置选项

```ini
# 30秒延迟
OnBootSec=30sec

# 60秒延迟
OnBootSec=60sec

# 2分钟延迟
OnBootSec=2min

# 5分钟延迟
OnBootSec=5min
```

### 管理命令

```bash
# 定时器管理
sudo systemctl start zookeeper.timer     # 启动定时器
sudo systemctl stop zookeeper.timer      # 停止定时器
sudo systemctl enable zookeeper.timer    # 开机自启定时器
sudo systemctl disable zookeeper.timer   # 禁用定时器

# 服务管理（通过定时器启动后）
sudo systemctl status zookeeper.service  # 查看服务状态
sudo systemctl stop zookeeper.service    # 停止服务（定时器仍活跃）

# 日志查看
journalctl -u zookeeper.timer -f         # 定时器日志
journalctl -u zookeeper.service -f       # 服务日志
```

### 监控和调试

```bash
# 查看定时器详细信息
sudo systemctl show zookeeper.timer

# 查看下次执行时间
sudo systemctl list-timers --all | grep zookeeper

# 立即触发服务（不等待定时器）
sudo systemctl start zookeeper.service

# 查看定时器和服务的依赖关系
systemctl list-dependencies zookeeper.timer
```

### 定时器优势

1. **不阻塞系统启动**：系统可以快速完成启动过程
2. **精确延迟控制**：可以设置任意延迟时间
3. **自动重启执行**：每次系统重启都会自动执行
4. **独立日志管理**：定时器和服务有独立的日志记录
5. **灵活控制**：可以随时启用、禁用或手动触发

### 使用场景

- 避免系统启动时的资源竞争
- 等待依赖服务完全就绪
- 分散多个服务的启动时间
- 减少系统启动时的负载峰值

### 注意事项

1. **JAVA_HOME路径**：确保脚本中的JAVA_HOME路径正确
   - 使用 `readlink -f $(which java)` 确认实际路径
   - 当前示例使用 `/usr/lib/jvm/java-8-openjdk-amd64`
   - 实际部署时需要根据系统调整为正确路径（如 `/usr/java/jdk1.8.0_241`）

2. **端口配置**：示例使用端口12181，避免与标准2181端口冲突

3. **集群部署**：对于ZooKeeper集群，每台服务器需要：
   - 不同的myid文件内容（1、2、3）
   - 相同的集群配置（server.1、server.2、server.3）
   - 确认各自的Java环境路径

通过systemd定时器，可以实现更加可靠和灵活的服务启动管理。
