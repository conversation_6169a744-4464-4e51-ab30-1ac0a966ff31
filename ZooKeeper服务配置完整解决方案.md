# ZooKeeper systemd服务配置方案

## 问题描述

ZooKeeper服务可以通过 `./bin/zkServer.sh start` 正常启动，但无法通过systemd管理。systemd启动时出现 `control process exited, code=exited status=1` 错误。

## 问题分析

systemd服务启动失败的主要原因：

1. **环境变量缺失**：systemd运行环境与用户shell环境不同，缺少JAVA_HOME等关键环境变量
2. **工作目录问题**：systemd启动时工作目录可能不正确，导致相对路径失效
3. **权限和路径解析**：systemd对脚本执行的环境要求更严格

## 解决方案：创建启动脚本

### 步骤1：创建启动脚本

创建ZooKeeper启动脚本：

```bash
sudo vim /usr/local/bin/zookeeper-start.sh
```

脚本内容：

```bash
#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
cd /app/zookeeper/zookeeper-3.4.8
./bin/zkServer.sh start
```

设置执行权限：

```bash
sudo chmod +x /usr/local/bin/zookeeper-start.sh
```

### 步骤2：创建停止脚本

创建ZooKeeper停止脚本：

```bash
sudo vim /usr/local/bin/zookeeper-stop.sh
```

脚本内容：

```bash
#!/bin/bash
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH
cd /app/zookeeper/zookeeper-3.4.8
./bin/zkServer.sh stop
```

设置执行权限：

```bash
sudo chmod +x /usr/local/bin/zookeeper-stop.sh
```

### 步骤3：配置systemd服务

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/zookeeper.service
```

服务配置内容：

```ini
[Unit]
Description=Apache ZooKeeper
After=network.target

[Service]
Type=forking
User=root
ExecStart=/usr/local/bin/zookeeper-start.sh
ExecStop=/usr/local/bin/zookeeper-stop.sh
PIDFile=/app/zookeeper/zookeeper-3.4.8/data/zookeeper_server.pid
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

### 步骤4：启用服务

重新加载systemd配置：

```bash
sudo systemctl daemon-reload
```

停止手动启动的ZooKeeper进程：

```bash
sudo /app/zookeeper/zookeeper-3.4.8/bin/zkServer.sh stop
```

启动systemd服务：

```bash
sudo systemctl start zookeeper
```

检查服务状态：

```bash
sudo systemctl status zookeeper
```

设置开机自启：

```bash
sudo systemctl enable zookeeper
```

### 步骤5：验证服务

检查ZooKeeper进程：

```bash
ps aux | grep zookeeper
```

检查端口监听（端口12181）：

```bash
netstat -tlnp | grep 12181
```

测试ZooKeeper连接：

```bash
echo "ruok" | nc localhost 12181
```

预期返回：`imok`

测试服务重启：

```bash
sudo systemctl restart zookeeper
sudo systemctl status zookeeper
```

## 服务管理命令

systemd服务管理：

```bash
sudo systemctl start zookeeper      # 启动服务
sudo systemctl stop zookeeper       # 停止服务
sudo systemctl restart zookeeper    # 重启服务
sudo systemctl status zookeeper     # 查看状态
sudo systemctl enable zookeeper     # 开机自启
sudo systemctl disable zookeeper    # 禁用自启
```

查看服务日志：

```bash
journalctl -u zookeeper -f          # 实时日志
journalctl -u zookeeper --since today  # 今日日志
```

## 配置文件信息

zoo.cfg配置（端口12181）：

```properties
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/app/zookeeper/zookeeper-3.4.8/data
clientPort=12181
```

## 故障排查

### 服务启动失败

检查systemd服务状态：

```bash
sudo systemctl status zookeeper -l
```

查看详细错误日志：

```bash
journalctl -u zookeeper --no-pager
```

### 脚本执行问题

手动测试启动脚本：

```bash
sudo /usr/local/bin/zookeeper-start.sh
```

检查脚本权限：

```bash
ls -la /usr/local/bin/zookeeper-*.sh
```

### 端口冲突

检查端口占用：

```bash
netstat -tlnp | grep 12181
lsof -i :12181
```

### Java环境问题

验证Java环境：

```bash
java -version
echo $JAVA_HOME
which java
```

### 配置文件检查

验证zoo.cfg语法：

```bash
cat /app/zookeeper/zookeeper-3.4.8/conf/zoo.cfg
```

检查数据目录权限：

```bash
ls -la /app/zookeeper/zookeeper-3.4.8/data/
```
